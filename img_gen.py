from diffusers import PixArtAlphaPipeline
import torch
from PIL import Image

# Load the PixArt pipeline (1024x1024 version)
pipe = PixArtAlphaPipeline.from_pretrained(
    "PixArt-alpha/PixArt-XL-2-512x512",
    torch_dtype=torch.float16,
    use_safetensors=True
)

# Move to GPU if available
pipe.to("cuda" if torch.cuda.is_available() else "cpu")

# Your text prompt
prompt = "A futuristic city skyline at sunset, highly detailed, 4K"

# Generate the image
image = pipe(prompt).images[0]

# Save the image
image.save("generated_image.png")

# Show the image (optional)
image.show()