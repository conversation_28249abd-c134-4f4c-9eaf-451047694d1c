[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ltx-video"
version = "0.1.2"
description = "A package for LTX-Video model"
authors = [
    { name = "LTX-Video Team", email = "<EMAIL>" }
]
requires-python = ">=3.10"
readme = "README.md"
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent"
]
dependencies = [
    "torch>=2.1.0",
    "diffusers>=0.28.2",
    "transformers>=4.47.2,<4.52.0",
    "sentencepiece>=0.1.96",
    "huggingface-hub~=0.30",
    "einops",
    "timm",
    "imageio (>=2.37.0,<3.0.0)",
    "av (>=15.1.0,<16.0.0)"
]

[project.optional-dependencies]
inference = [
    "imageio[ffmpeg]",
    "av",
    "torchvision"
]
test = [
    "pytest",
]

[tool.setuptools.packages.find]
include = ["ltx_video*"]

[tool.setuptools.package-data]
ltx_video = ["configs/*.yaml"]
